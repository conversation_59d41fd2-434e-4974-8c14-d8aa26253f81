package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/cache"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util/triton"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util/triton/types"
	umsConfig "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/config"
	umsModel "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/inquirer/infer/model"
	umsUtil "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/util"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/common"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/dao"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/domain"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/entity"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/s3"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util"
)

func CreateModel(req *entity.CreateModelRequest) (int32, error) {
	if req.Model.ServiceID == 0 {
		return 0, errors.New(common.InvalidServiceID)
	}

	serviceID := req.Model.ServiceID
	paths := []string{req.Model.FilePath}
	model := &domain.ModelEntity{
		Name:              req.Model.Name,
		Version:           req.Model.Version,
		Region:            req.Model.Region,
		ServiceID:         req.Model.ServiceID,
		Path:              req.Model.FilePath,
		LoaderType:        req.Model.LoaderType,
		Status:            0,
		PostprocessConfig: req.Model.PostprocessConfig,
		ModelConfig:       req.Model.ModelConfig,
		CreateTime:        uint64(time.Now().Unix()),
		UpdateTime:        uint64(time.Now().Unix()),
		IsValid:           1,
		Creator:           req.Model.Creator,
		Updater:           req.Model.Updater,
	}
	s, err := dao.GetService(serviceID)
	if err != nil {
		return 0, err
	}
	err = dao.CreateModel(model)
	if err != nil {
		return 0, err
	}

	processModelFileAsync(filterNotDownloadedPaths(s.Name, paths), s.Name, model)

	return 0, nil
}

func ListModel(req *entity.ListModelRequest) (*entity.ListModelResponse, error) {
	ms, err := dao.ListModel(req.ServiceID, req.LoaderType, req.Region, req.Limit, req.Offset, req.SortParams)
	if err != nil {
		return nil, err
	}
	models := make([]*entity.Model, 0)
	for _, m := range ms {
		mm := &entity.Model{
			ID:                m.ID,
			Name:              m.Name,
			Region:            m.Region,
			Version:           m.Version,
			LoaderType:        m.LoaderType,
			ServiceID:         m.ServiceID,
			ServiceName:       m.ServiceName,
			FilePath:          m.Path,
			ModelStatus:       m.Status,
			PostprocessConfig: m.PostprocessConfig,
			ModelConfig:       m.ModelConfig,
			CreateTime:        util.Timestamp2String(int64(m.CreateTime)),
			UpdateTime:        util.Timestamp2String(int64(m.UpdateTime)),
			IsValid:           m.IsValid,
			Creator:           m.Creator,
			Updater:           m.Updater,
		}
		models = append(models, mm)
	}

	total, err := dao.CountModel(req.ServiceID, req.LoaderType, req.Region)
	if err != nil {
		return nil, err
	}
	return &entity.ListModelResponse{
		Total:  total,
		Models: models,
	}, nil
}

func UpdateModel(reqModel entity.Model) (int32, error) {
	if reqModel.ID == 0 {
		return 0, errors.New(common.InvalidModelID)
	}

	dbModel, err := dao.GetModelWithServiceByID(reqModel.ID)
	if err != nil {
		return 0, err
	}
	if dbModel == nil {
		return 0, errors.New(common.ModelNotExist)
	}
	processModelFileAsync([]string{reqModel.FilePath}, dbModel.ServiceName, dbModel.ToModelEntity())
	mergedModel := &domain.ModelEntity{
		ID:                dbModel.ID,
		Version:           reqModel.Version,
		Region:            reqModel.Region,
		ServiceID:         reqModel.ServiceID,
		Path:              reqModel.FilePath,
		LoaderType:        reqModel.LoaderType,
		Status:            reqModel.ModelStatus,
		PostprocessConfig: reqModel.PostprocessConfig,
		ModelConfig:       reqModel.ModelConfig,
		CreateTime:        dbModel.CreateTime,
		UpdateTime:        uint64(time.Now().Unix()),
		IsValid:           dbModel.IsValid,
		Creator:           dbModel.Creator,
		Updater:           reqModel.Updater,
	}
	err = dao.UpdateModel(mergedModel, util.Struct2Map(mergedModel))
	if err != nil {
		return 0, err
	}
	return reqModel.ID, nil
}

func GetModelInfoByID(id int32) (*entity.Model, error) {
	if id == 0 {
		return nil, errors.New(common.InvalidModelID)
	}

	modelInfo, err := dao.GetModelWithServiceByID(id)
	if err != nil {
		return nil, err
	}

	return &entity.Model{
		ID:                modelInfo.ID,
		Name:              modelInfo.Name,
		Region:            modelInfo.Region,
		Version:           modelInfo.Version,
		LoaderType:        modelInfo.LoaderType,
		ServiceID:         modelInfo.ServiceID,
		ServiceName:       modelInfo.ServiceName,
		FilePath:          modelInfo.Path,
		ModelStatus:       modelInfo.Status,
		PostprocessConfig: modelInfo.PostprocessConfig,
		ModelConfig:       modelInfo.ModelConfig,
		CreateTime:        util.Timestamp2String(int64(modelInfo.CreateTime)),
		UpdateTime:        util.Timestamp2String(int64(modelInfo.UpdateTime)),
		IsValid:           modelInfo.IsValid,
	}, nil
}

func GetModelStatisticsByID(id int32) ([]*domain.ModelStatistics, error) {
	if id == 0 {
		return nil, errors.New(common.InvalidModelID)
	}

	statistics, err := dao.GetModelStatisticsByID(id)
	if err != nil {
		return nil, err
	}
	modelWithService, err := dao.GetModelWithServiceByID(id)
	if err != nil {
		return nil, err
	}
	features, err := GetModelFinalFeatures(modelWithService.ServiceName, modelWithService.Path, modelWithService.LoaderType)
	if err == nil {
		statistics = append(statistics, &domain.ModelStatistics{
			Source: "final",
			Count:  len(features),
		})
	}

	return statistics, nil
}

func UploadModel(req *entity.UploadModelRequest) (string, error) {
	serviceName := strconv.Quote(req.ServiceName)
	versionName := strconv.Quote(req.VersionName)
	modelRegion := strconv.Quote(req.ModelRegion)
	loader := strconv.Quote(req.Loader)
	hdfsFilePath := strconv.Quote(req.HdfsFilePath)

	commandArgs := []string{serviceName, versionName, modelRegion, loader, hdfsFilePath}
	cmd := exec.Command("./scripts/hdfs_to_s3.sh", commandArgs...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		util.Logger().WithField("err", err).Error("upload model failed")
		return "", err
	}

	return string(output), nil
}

func GetModelFinalFeatures(serviceName, modelPath, modelLoaderType string) ([]string, error) {
	modelFileUpdated, err := isModelFileUpdated(serviceName, modelPath, modelLoaderType)
	if err != nil {
		return nil, err
	}

	if modelFileUpdated {
		c := umsConfig.Model{LoaderType: modelLoaderType, ServiceName: serviceName, FilePath: modelPath}
		model, err := umsModel.LoadModel(context.Background(), &c)
		if err != nil {
			return nil, err
		}

		finalFeatures := model.GetFeatures()
		go func() {
			err := cache.SetFinalFeatures(serviceName, modelPath, modelLoaderType, finalFeatures)
			if err != nil {
				util.Logger().WithField("err", err).Error("cache final features failed")
			}
		}()

		return finalFeatures, nil
	}

	return cache.GetFinalFeatures(serviceName, modelPath, modelLoaderType)
}

func isModelFileUpdated(serviceName, modelPath, modelLoaderType string) (bool, error) {
	modelFilePath := umsUtil.LocalModelPath(serviceName, modelPath)
	fileModTime, err := util.GetFileModTime(modelFilePath)
	if err != nil {
		// load file from s3
		err = s3.GetClient().GetObject(serviceName, modelPath)
		if err != nil {
			return false, err
		}
		fileModTime, err = util.GetFileModTime(modelFilePath)
		if err != nil {
			return false, err
		}
	}

	cachedModTime, err := cache.GetModelFileUpdateTime(serviceName, modelPath, modelLoaderType)
	if err != nil {
		return false, err
	}

	modelFileIsUpdated := fileModTime.After(*cachedModTime)
	if modelFileIsUpdated {
		err = cache.SetModelFileUpdateTime(serviceName, modelPath, modelLoaderType, &fileModTime)
		if err != nil {
			return false, err
		}
	}

	return modelFileIsUpdated, nil
}

func processModelFileAsync(paths []string, serviceName string, model *domain.ModelEntity) {
	for _, p := range paths {
		go func() {
			err := s3.GetClient().GetObject(serviceName, p)
			if err != nil {
				util.Logger().WithField("err", err).Error("get object failed")
				return
			}

			if util.IsDeployedInTriton(model.LoaderType) {
				if err := createTritonModelConfig(serviceName, p, model); err != nil {
					util.Logger().WithField("err", err).Error("create triton model config failed")
					return
				}
			}
		}()
	}
}

func createTritonModelConfig(serviceName string, modelPath string, model *domain.ModelEntity) error {
	var modelFile *os.File
	var err error

	switch model.LoaderType {
	case common.LoaderTypePyTorch:
	// Do nothing as we don't extract information from pytorch model.
	default:
		modelFile, err = os.Open(umsUtil.LocalModelPath(serviceName, modelPath))
		if err != nil {
			return err
		}
		defer modelFile.Close()
	}

	modelConfig := &types.ModelConfig{}
	err = json.Unmarshal([]byte(model.ModelConfig), modelConfig)
	if err != nil {
		return err
	}

	configPath := util.LocalModelConfigPath(serviceName, modelPath)

	configOptions := &types.ConfigGenerationOptions{
		ModelName:           fmt.Sprintf("%s_%s_%s", serviceName, model.Region, model.Version),
		ModelFileName:       filepath.Base(modelPath),
		MaxBatchSize:        modelConfig.MaxBatchSize,
		Inputs:              modelConfig.Input,
		Outputs:             modelConfig.Output,
		VersionPolicy:       modelConfig.VersionPolicy,
		InstanceGroups:      modelConfig.InstanceGroup,
		Backend:             modelConfig.Backend,
		EnableResponseCache: modelConfig.ResponseCache != nil && modelConfig.ResponseCache.Enable,
		EnableDecoupledMode: modelConfig.ModelTransactionPolicy.Decoupled,
		KnownModelType:      common.LoaderTypeToModelType[model.LoaderType],
	}

	err = triton.GenerateConfigFile(modelFile, configPath, configOptions)
	if err != nil {
		return err
	}
	return nil
}

func filterNotDownloadedPaths(serviceName string, paths []string) []string {
	notDownloadedPaths := make([]string, 0)

	for _, path := range paths {
		localPath := umsUtil.LocalModelPath(serviceName, path)

		if fileExist, err := util.IsFileExist(localPath); fileExist && err == nil {
			notDownloadedPaths = append(notDownloadedPaths, path)
		}
	}

	return notDownloadedPaths
}

func GetModelFiles() ([]*entity.ModelFile, error) {
	modelsFromDB, err := dao.ListModel(nil, nil, nil, 0, 0, nil)
	if err != nil {
		util.Logger().WithField("get model list from db", "").Error(err.Error())
		return nil, err
	}

	result := make([]*entity.ModelFile, 0)
	for _, model := range modelsFromDB {
		mf := &entity.ModelFile{
			ServiceID:    model.ServiceID,
			ServiceName:  model.ServiceName,
			ModelID:      model.ID,
			ModelName:    model.Name,
			Region:       model.Region,
			ModelType:    model.LoaderType,
			ModelVersion: model.Version,
			ModelPath:    model.Path,
		}
		result = append(result, mf)

		if strings.Contains(model.LoaderType, umsConfig.LoaderXGBoost) {
			featureMF := &entity.ModelFile{
				ServiceID:    model.ServiceID,
				ServiceName:  model.ServiceName,
				ModelID:      model.ID,
				ModelName:    model.Name,
				Region:       model.Region,
				ModelType:    model.LoaderType,
				ModelVersion: model.Version,
				ModelPath:    umsUtil.FeatureMapPath(model.Path),
			}
			result = append(result, featureMF)
		}
	}
	for i, m := range result {
		checkSum := CalCheckSum(m.ServiceName, m.ModelPath)
		result[i].Checksum = checkSum
	}
	return result, nil
}

func UploadS3File(file io.Reader, serviceName string, modelInfo *entity.Model, fileExt, fileType string, size int64,
	forceUpload bool) (string, error) {
	filePath := ""

	switch modelInfo.LoaderType {
	default:
		if fileType == "model" {
			filePath = fmt.Sprintf("/%s/data/model/%s/%s%s", serviceName, modelInfo.Region, modelInfo.Version, fileExt)
		} else if fileType == "feature" {
			filePath = fmt.Sprintf("/%s/data/model/%s/%s_fmap.json", serviceName, modelInfo.Region, modelInfo.Version)
		}

	case common.LoaderTypePyTorch: // TODO: Use umsConfig.LoaderPytorch
		// Always keeps the model version in Triton as `1` because multiple versions in UMS are dependant.
		tritonModelName := fmt.Sprintf("%s_%s_%s", serviceName, modelInfo.Region, modelInfo.Version)
		filePath = fmt.Sprintf("/%s/data/model/%s/%s/model%s", serviceName, tritonModelName, "1", fileExt)
		configurationPath := util.LocalModelConfigPath(serviceName, fmt.Sprintf("/data/model/%s/config.pbtxt", tritonModelName))
		configurationOptions := generateModelConfigOptions(tritonModelName, fileExt, modelInfo)
		err := triton.GenerateConfigFile(file, configurationPath, configurationOptions)
		if err != nil {
			return "", err
		}
	}

	s3Client := s3.GetClient()
	exist, err := s3Client.IsObjectExist(filePath)
	if err != nil {
		return "", err
	}
	// rename first
	if exist {
		if !forceUpload {
			return "-1", nil
		}

		err = s3Client.RenameObject(filePath)
		if err != nil {
			return "", err
		}
	}
	// upload
	path, err := s3Client.UploadFile(file, filePath, size)
	if err != nil {
		return "", err
	}

	return path, nil
}

func generateModelConfigOptions(modelName string, fileExt string, modelInfo *entity.Model) *types.ConfigGenerationOptions {
	modelConfig := &types.ModelConfig{}
	if modelInfo.ModelConfig != "" {
		err := json.Unmarshal([]byte(modelInfo.ModelConfig), modelConfig)
		if err != nil {
			util.Logger().WithField("err", err).Error("unmarshal model config failed")
		}
	}

	configurationOptions := &types.ConfigGenerationOptions{
		ModelName:           modelName,
		ModelFileName:       fmt.Sprintf("model.%s", fileExt),
		MaxBatchSize:        modelConfig.MaxBatchSize,
		Inputs:              modelConfig.Input,
		Outputs:             modelConfig.Output,
		VersionPolicy:       modelConfig.VersionPolicy,
		InstanceGroups:      modelConfig.InstanceGroup,
		Backend:             modelConfig.Backend,
		EnableResponseCache: modelConfig.ResponseCache != nil && modelConfig.ResponseCache.Enable,
		EnableDecoupledMode: modelConfig.ModelTransactionPolicy != nil && modelConfig.ModelTransactionPolicy.Decoupled,
		KnownModelType:      common.LoaderTypeToModelType[modelInfo.LoaderType],
	}

	if configurationOptions.VersionPolicy == nil {
		configurationOptions.VersionPolicy = triton.DefaultConfigGenerationOptions.VersionPolicy
	}

	if len(configurationOptions.InstanceGroups) == 0 {
		configurationOptions.InstanceGroups = triton.DefaultConfigGenerationOptions.InstanceGroups
	}

	switch modelInfo.LoaderType {
	case umsConfig.LoaderLightGBM:
		configurationOptions.Options = &types.FILOptions{
			ModelType:    "lightgbm",
			Threshold:    0.5,
			PredictProba: true,
			OutputClass:  true,
		}

	case umsConfig.LoaderXGBoost:
		configurationOptions.Options = &types.FILOptions{
			ModelType:    "xgboost",
			Threshold:    0.5,
			PredictProba: true,
			OutputClass:  true,
		}

	case umsConfig.LoaderXGBoostV17:
		configurationOptions.Options = &types.FILOptions{
			ModelType:    "xgboost_json",
			Threshold:    0.5,
			PredictProba: true,
			OutputClass:  true,
		}

	case common.LoaderTypePyTorch:
		pytorchOptions := &types.PyTorchOptions{
			InferenceMode:      true,
			EnableJitExecutor:  true,
			EnableJitProfiling: true,
		}

		if modelConfig.Parameters == nil {
			if modelConfig.Parameters["DISABLE_OPTIMIZED_EXECUTION"] != nil {
				pytorchOptions.DisableOptimizedExecution = modelConfig.Parameters["DISABLE_OPTIMIZED_EXECUTION"].StringValue == "true"
			}
			if modelConfig.Parameters["INFERENCE_MODE"] != nil {
				pytorchOptions.InferenceMode = modelConfig.Parameters["INFERENCE_MODE"].StringValue == "true"
			}
			if modelConfig.Parameters["DISABLE_CUDNN"] != nil {
				pytorchOptions.DisableCudnn = modelConfig.Parameters["DISABLE_CUDNN"].StringValue == "true"
			}
			if modelConfig.Parameters["ENABLE_WEIGHT_SHARING"] != nil {
				pytorchOptions.EnableWeightSharing = modelConfig.Parameters["ENABLE_WEIGHT_SHARING"].StringValue == "true"
			}
			if modelConfig.Parameters["ENABLE_CACHE_CLEANING"] != nil {
				pytorchOptions.EnableCacheCleaning = modelConfig.Parameters["ENABLE_CACHE_CLEANING"].StringValue == "true"
			}
			if modelConfig.Parameters["INTER_OP_THREAD_COUNT"] != nil {
				pytorchOptions.InterOpThreadCount, _ = strconv.Atoi(modelConfig.Parameters["INTER_OP_THREAD_COUNT"].StringValue)
			}
			if modelConfig.Parameters["INTRA_OP_THREAD_COUNT"] != nil {
				pytorchOptions.IntraOpThreadCount, _ = strconv.Atoi(modelConfig.Parameters["INTRA_OP_THREAD_COUNT"].StringValue)
			}
			if modelConfig.Parameters["ENABLE_JIT_EXECUTOR"] != nil {
				pytorchOptions.EnableJitExecutor = modelConfig.Parameters["ENABLE_JIT_EXECUTOR"].StringValue == "true"
			}
			if modelConfig.Parameters["ENABLE_JIT_PROFILING"] != nil {
				pytorchOptions.EnableJitProfiling = modelConfig.Parameters["ENABLE_JIT_PROFILING"].StringValue == "true"
			}
			if modelConfig.Parameters["NUM_THREADS"] != nil {
				pytorchOptions.NumThreads, _ = strconv.Atoi(modelConfig.Parameters["NUM_THREADS"].StringValue)
			}
			if modelConfig.Parameters["NUM_INTEROP_THREADS"] != nil {
				pytorchOptions.NumInteropThreads, _ = strconv.Atoi(modelConfig.Parameters["NUM_INTEROP_THREADS"].StringValue)
			}
			if modelConfig.Parameters["TORCH_COMPILE_OPTIONAL_PARAMETERS"] != nil {
				jsonData := []byte(modelConfig.Parameters["TORCH_COMPILE_OPTIONAL_PARAMETERS"].StringValue)
				err := json.Unmarshal(jsonData, &pytorchOptions.TorchCompile)
				if err != nil {
					util.Logger().WithField("err", err).Error("unmarshal torch compile parameters failed")
				}
			}
			if modelConfig.Parameters["TORCH_COMPILE_OPTIONAL_PARAMETERS"] != nil {
				jsonData := []byte(modelConfig.Parameters["TORCH_COMPILE_OPTIONAL_PARAMETERS"].StringValue)
				err := json.Unmarshal(jsonData, &pytorchOptions.TorchCompile)
				if err != nil {
					util.Logger().WithField("err", err).Error("unmarshal torch compile parameters failed")
				}
			}
		}

		configurationOptions.Options = pytorchOptions
	}

	return configurationOptions
}

func GetModelsStatistics(fromDate int64, groupBy string) ([]*domain.ModelsStatistics, error) {
	return dao.GetModelsStatistics(fromDate, groupBy)
}

func GetModelFileURL(modelID int32) (string, error) {
	model, err := dao.GetModelWithServiceByID(modelID)
	if err != nil {
		util.Logger().WithField("get model by id", modelID).Error(err.Error())
		return "", err
	}

	filePath := umsUtil.RemoteModelPath(model.ServiceName, model.Path)
	objectURL, err := s3.GetClient().GetPreSignedGetObject(filePath)
	if err != nil {
		return "", err
	}

	return objectURL, nil
}

func CalCheckSum(serviceName, filePath string) string {
	localPath := umsUtil.LocalModelPath(serviceName, filePath)
	if _, err := os.Stat(localPath); err != nil {
		return ""
	}
	file, err := os.Open(localPath)
	if err != nil {
		return ""
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return ""
	}
	return hex.EncodeToString(hash.Sum(nil))
}
